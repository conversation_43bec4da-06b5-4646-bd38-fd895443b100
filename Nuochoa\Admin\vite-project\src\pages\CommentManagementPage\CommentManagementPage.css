.comment-management {
  padding: 20px;
}

.comment-management h2 {
  text-align: center;
}

.review-list {
  margin-top: 20px;
}

.review-item {
  border: 1px solid #ccc;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-user {
  font-weight: bold;
}

.review-actions button {
  padding: 5px 10px;
  margin-left: 10px;
  cursor: pointer;
  font-size: 14px;
}

.edit-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
}

.delete-btn {
  background-color: red;
  color: white;
  border: none;
}

.report-btn {
  background-color: orange;
  color: white;
  border: none;
}

.review-date {
  font-size: 12px;
  color: #666;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.modal h3 {
  margin-bottom: 15px;
}

.modal textarea {
  width: 100%;
  height: 100px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
}

.save-btn, .cancel-btn {
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
}

.cancel-btn {
  background-color: #f44336;
  color: white;
  border: none;
}

.save-btn:hover, .cancel-btn:hover {
  opacity: 0.8;
}
