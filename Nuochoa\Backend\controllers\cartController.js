import cartModel from "../models/Cart.js";  // Import mô hình Cart
import userModel from "../models/UserModel.js";  // Import mô hình User

const extractUserId = (req) => {
  return req.user?.uid || req.user?.id || req.body.userId;
};


// Thêm sản phẩm vào giỏ hàng
const addToCart = async (req, res) => {
  try {
    const { itemId, size, quantity, productData } = req.body;
    const userId = req.user?.uid || req.user?.id || req.body.userId;


    // Tìm giỏ hàng theo userId (firebaseUid)
    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      cart = new cartModel({
        userId, // firebaseUid
        cartData: [{
            itemId,
          name: productData.name,
          image: productData.image,
          originalPrice: productData.originalPrice || productData.price,
          price: productData.price,
          hasPromotion: productData.hasPromotion || false,
          promotion: productData.promotion || null,
          discountPercentage: productData.promotion?.discountPercentage || 0,
          size: size,
          quantity: quantity
        }]
      });
    } else {
      let cartData = cart.cartData || [];
      const existingItemIndex = cartData.findIndex(item => item.itemId === itemId && item.size === size)


      if (existingItemIndex !== -1) {
        // Cộng dồn số lượng nếu sản phẩm đã có trong giỏ
        cartData[existingItemIndex].quantity += quantity;
      } else {
        // Thêm mới sản phẩm vào giỏ
        cartData.push({
          itemId,
          name: productData.name,
          image: productData.image,
          originalPrice: productData.originalPrice || productData.price,
          price: productData.price,
          hasPromotion: productData.hasPromotion || false,
          promotion: productData.promotion || null,
          discountPercentage: productData.promotion?.discountPercentage || 0,
          size: size,
          quantity: quantity
        });
      }

      cart.cartData = cartData;
    }

    await cart.save();
    res.json({ success: true, message: "Đã thêm vào giỏ hàng" });
    console.log("🛒 Add to cart: userId =", userId, "itemId =", itemId, "size =", size);


  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi thêm vào giỏ hàng" });
  }
};



const removeFromCart = async (req, res) => {
  try {
    const { itemId, size } = req.body;
    const userId = extractUserId(req);

    if (!userId) {
      return res.status(401).json({ success: false, message: "Không xác định được người dùng" });
    }

    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    cart.cartData = cart.cartData.filter(item => !(item.itemId === itemId && item.size === size));

    await cart.save();
    res.json({ success: true, message: "Đã xóa khỏi giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa khỏi giỏ hàng" });
  }
};



const updateCartQuantity = async (req, res) => {
  try {
    const { itemId, size, quantity } = req.body;
    const userId = extractUserId(req);

    if (!userId) {
      return res.status(401).json({ success: false, message: "Không xác định được người dùng" });
    }

    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    const itemIndex = cart.cartData.findIndex(item => item.itemId === itemId && item.size === size)


    if (itemIndex !== -1) {
      if (quantity <= 0) {
        cart.cartData.splice(itemIndex, 1); // Xóa nếu = 0
      } else {
        cart.cartData[itemIndex].quantity = quantity;
      }
      await cart.save();
      return res.json({ success: true, message: "Đã cập nhật giỏ hàng" });
    }

    res.json({ success: false, message: "Không tìm thấy sản phẩm trong giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.json({ success: false, message: "Lỗi khi cập nhật giỏ hàng" });
  }
};



// Lấy giỏ hàng của user
const getCart = async (req, res) => {
  try {
    const { userId } = req.body;

    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    res.json({ success: true, cartData: cart.cartData });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi lấy giỏ hàng" });
  }
};


// Đồng bộ giỏ hàng từ client lên server
const syncCart = async (req, res) => {
  try {
    const { userId, cartData } = req.body;

    // Kiểm tra xem người dùng có giỏ hàng hay không
    let cart = await cartModel.findOne({ userId });

    if (!cart) {
      // Nếu không có giỏ hàng, tạo mới giỏ hàng cho người dùng
      cart = new cartModel({
        userId,
        cartData: cartData || []
      });
    } else {
      // Nếu đã có giỏ hàng, cập nhật giỏ hàng
      cart.cartData = cartData || [];
    }

    // Lưu lại giỏ hàng
    await cart.save();
    res.json({ success: true, message: "Đã đồng bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi đồng bộ giỏ hàng" });
  }
};


// Xóa toàn bộ giỏ hàng
const clearCart = async (req, res) => {
  try {
    const { userId } = req.body;

    // Kiểm tra xem người dùng có giỏ hàng hay không
    let cart = await cartModel.findOne({ userId });

    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    // Xóa toàn bộ giỏ hàng
    cart.cartData = [];
    await cart.save();

    res.json({ success: true, message: "Đã xóa toàn bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa giỏ hàng" });
  }
};


export {
  addToCart,
  removeFromCart,
  updateCartQuantity,
  getCart,
  syncCart,
  clearCart
};
