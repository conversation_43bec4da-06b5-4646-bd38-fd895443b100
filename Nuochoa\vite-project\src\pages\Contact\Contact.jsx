import React from 'react';
import './Contact.css';

const Contact = () => {

  return (
    <div className="contact-page">
      <div className="contact-container">
        {/* Header Section */}
        <div className="contact-header">
          <h1>Liên hệ với chúng tôi</h1>
          <p>Chúng tôi luôn sẵn sàng hỗ trợ bạn qua các kênh liên lạc dưới đây!</p>
        </div>

        <div className="contact-content">
          {/* Contact Info */}
          <div className="contact-info">
            <div className="info-card">
              <div className="info-icon">📞</div>
              <h3>Zalo</h3>
              <p>0399796850</p>
              <span className="info-note"><PERSON>ên hệ qua Zalo</span>
            </div>

            <div className="info-card">
              <div className="info-icon">📧</div>
              <h3>Email</h3>
              <p><EMAIL></p>
              <span className="info-note">Phản hồi trong 24h</span>
            </div>

            <div className="info-card">
              <div className="info-icon">📍</div>
              <h3>Địa chỉ</h3>
              <p>Huyện Càng Long, Tỉnh Trà Vinh</p>
              <span className="info-note">Mở cửa 9:00 - 21:00</span>
            </div>

           
          
          </div>

          {/* Social Contact Section */}
          <div className="contact-form-section">
            <div className="form-header">
              <h2>Liên hệ qua mạng xã hội</h2>
              <p>Chọn kênh liên lạc phù hợp để được hỗ trợ nhanh chóng</p>
            </div>

            <div className="social-contact-grid">
              <div className="social-contact-card">
                <div className="social-icon facebook-icon">📘</div>
                <h3>Facebook</h3>
                <p>Nhắn tin trực tiếp qua Fanpage</p>
                <a
                  href="https://www.facebook.com/nguyen.xuyen.369765"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-btn facebook-btn"
                >
                  Liên hệ Facebook
                </a>
              </div>

              <div className="social-contact-card">
                <div className="social-icon instagram-icon">📷</div>
                <h3>Instagram</h3>
                <p>Theo dõi và nhắn tin qua Instagram</p>
                <a
                  href="https://www.instagram.com/?hl=en"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-btn instagram-btn"
                >
                  Liên hệ Instagram
                </a>
              </div>

              <div className="social-contact-card">
                <div className="social-icon zalo-icon">💬</div>
                <h3>Zalo</h3>
                <p>Chat trực tiếp qua Zalo: 0399796850</p>
                <a
                  href="https://zalo.me/0399796850"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="contact-btn zalo-btn"
                >
                  Liên hệ Zalo
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="faq-section">
          <h2>Câu hỏi thường gặp</h2>
          <div className="faq-grid">
            <div className="faq-item">
              <h4>🚚 Chính sách giao hàng</h4>
              <p>Miễn phí giao hàng cho đơn từ 500k. Giao hàng trong 1-3 ngày làm việc.</p>
            </div>
            <div className="faq-item">
              <h4>🔄 Chính sách đổi trả</h4>
              <p>Đổi trả trong 7 ngày nếu sản phẩm lỗi hoặc không đúng mô tả.</p>
            </div>
            <div className="faq-item">
              <h4>💳 Phương thức thanh toán</h4>
              <p>Hỗ trợ thanh toán qua Visa, Mastercard, MoMo, ZaloPay và COD.</p>
            </div>
            <div className="faq-item">
              <h4>🎁 Chương trình khuyến mãi</h4>
              <p>Cập nhật khuyến mãi mới nhất trên website và fanpage Facebook.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
